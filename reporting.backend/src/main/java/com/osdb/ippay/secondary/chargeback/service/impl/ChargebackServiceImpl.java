package com.osdb.ippay.secondary.chargeback.service.impl;

import static lombok.AccessLevel.PRIVATE;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.persistence.criteria.Predicate;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import com.osdb.ippay.primary.user.repository.entity.User;
import com.osdb.ippay.secondary.chargeback.repository.ChargebackRepository;
import com.osdb.ippay.secondary.chargeback.repository.entity.Chargeback;
import com.osdb.ippay.secondary.chargeback.service.ChargebackFilter;
import com.osdb.ippay.secondary.chargeback.service.ChargebackService;

import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = PRIVATE, makeFinal = true)
public class ChargebackServiceImpl implements ChargebackService {

    ChargebackRepository chargebackRepository;

    @Override
    public Page<Chargeback> find(User authUser, ChargebackFilter filter, Pageable pageable) {
        Specification<Chargeback> specification = filterBy(authUser, filter);

        PageRequest pageRequest = PageRequest.of(
                pageable.getPageNumber(),
                pageable.getPageSize(),
                getSort(pageable));

        return chargebackRepository.findAll(specification, pageRequest);
    }

    @Override
    public List<Chargeback> find(User authUser, ChargebackFilter filter) {
        Specification<Chargeback> specification = filterBy(authUser, filter);
        return chargebackRepository.findAll(specification);
    }

    @Override
    public List<String> findDates(String merchantId) {
        return chargebackRepository
                .findInitialDates(merchantId).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private String intToFormattedString(int value) {
        String strValue = Integer.toString(value);
        return strValue.trim().length() == 2 ? strValue : "0" + strValue;

    }

    private String localDateToStr(LocalDate localDate) {
        String year = String.valueOf(localDate.getYear());
        String month = intToFormattedString(localDate.getMonthValue());
        String day = intToFormattedString(localDate.getDayOfMonth());

        return String.format("%s%s%s", month, day, year);
    }

    private Specification<Chargeback> filterBy(User authUser, ChargebackFilter filter) {
        return (r, rq, cb) -> {

            Predicate merchantIdPredicate = StringUtils.isNotBlank(filter.getMerchantId())
                    ? cb.equal(r.join("merchant").get("merchantId"), filter.getMerchantId())
                    : cb.conjunction();
            Predicate datePredicate = filter.getFromDate() != null
                    ? cb.between(
                    cb.function("STR_TO_DATE", java.sql.Date.class, r.get("dateResolved"), cb.literal("%m%d%Y")),
                    java.sql.Date.valueOf(filter.getFromDate()),
                    java.sql.Date.valueOf(filter.getToDate()))
                    : cb.conjunction();
            Predicate merchantIdsPredicate = authUser.isNotAdminAndLevel1Support()
                    ? cb.in(r.join("merchant").get("merchantId"))
                            .value(Optional.ofNullable(filter.getMerchantIds()).orElse(Collections.emptyList()))
                    : cb.conjunction();

            Predicate arnPredicate = StringUtils.isNotBlank(filter.getArn())
                    ? cb.like(cb.lower(r.get("acquirerReferenceNumber")), "%" + filter.getArn().toLowerCase() + "%")
                    : cb.conjunction();

            return cb.and(
                    merchantIdPredicate,
                    datePredicate,
                    merchantIdsPredicate,
                    arnPredicate);
        };
    }

    private Sort getSort(Pageable pageable) {
        Sort.Order order = pageable.getSort().get()
                .findFirst()
                .orElse(Sort.Order.desc("dateResolved"));

        Sort.Direction direction = order.getDirection();
        String property = order.getProperty();

        return switch (property) {
            case "merchantName" -> Sort.by(direction, "merchant.merchantName");
            case "merchantId" -> Sort.by(direction, "merchant.merchantId");
            default -> Sort.by(order);
        };
    }
}
