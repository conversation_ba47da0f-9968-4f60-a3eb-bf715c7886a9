package com.osdb.ippay.secondary.chargeback.service;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

import static lombok.AccessLevel.PRIVATE;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = PRIVATE)
@Builder
public class ChargebackFilter {

    String merchantId;

    @NotNull(message = "Missing required parameter: 'fromDate'.")
    @Parameter(example = "2017-01-25")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    LocalDate fromDate;

    @NotNull(message = "Missing required parameter: 'toDate'.")
    @Parameter(example = "2020-07-16")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    LocalDate toDate;

    @Parameter(hidden = true)
    @JsonIgnore
    List<String> merchantIds;

    String arn;

}
