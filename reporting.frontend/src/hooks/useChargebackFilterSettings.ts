import { useCallback } from "react";

import { ChargebackFilterSettings, default_ChargebackFilterSettings } from "../common/entities";
import { useFilterQueryParams } from "./useFilterQueryParams";

export const useChargebackFilterSettings = () => {
  const {
    filterQueryParams,
    setFilterQueryParams,
    resetAllFilterQueryParams,
    resetAllAdditionalFilterQueryParams,
  } = useFilterQueryParams();

  const onSubmit = useCallback(
    (settings: ChargebackFilterSettings) => {
      setFilterQueryParams(settings, "replaceIn");
    },
    [setFilterQueryParams]
  );

  const onResetFilter = useCallback(() => {
    resetAllFilterQueryParams();
  }, [resetAllFilterQueryParams]);

  const onResetDrawerFilter = useCallback(() => {
    resetAllAdditionalFilterQueryParams();
  }, [resetAllAdditionalFilterQueryParams]);

  return {
    filterSettings: { ...default_ChargebackFilterSettings, ...filterQueryParams } as ChargebackFilterSettings,
    onSubmit,
    onResetFilter,
    onResetDrawerFilter,
  };
};
