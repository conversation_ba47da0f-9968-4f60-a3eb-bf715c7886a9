import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";

import { Box, FormControl, InputLabel, MenuItem, Select, Button } from "@material-ui/core";
import axios, { AxiosResponse } from "axios";
import { filter } from "lodash";
import { useQueryParams, StringParam, NumberParam, withDefault } from "use-query-params";

import { PaginatedResponse } from "../../../api/types";
import { ChargebackReportFilterSettings, default_ChargebackReportFilterSettings } from "../../../common/entities";
import { Breadcrumbs } from "../../../components/Breadcrumbs";
import { PageTitle } from "../../../components/PageTitle";
import { AdvancedTable } from "../../../components/tables/AdvancedTable";
import { AlertApi, ChargebackReportDataDto } from "../../../domain/risk/api/alerts-api";
import { ChargebacksReportFilter } from "../../../domain/risk/components/ChargebacksReportFilter";
import { AvailableUserSettingsTypes } from "../../../domain/user-config/api/user-config-api";
import { NewObjectUtils } from "../../../helpers/NewObjectUtils";
import { useFetch } from "../../../hooks/use_ACH_CC_DataQuery";
import { DashboardRouteNames } from "../../../routes/constants";
import { ActionButtonsContainer } from "../../../styles/Global.styles";
import { ExportButtonLink } from "../../../components/buttons/ExportButton";

const breadcrumbs = [{ to: DashboardRouteNames.ROOT, label: "Dashboard" }, { label: "Chargebacks Report" }];

const tableColumns = [
  { field: "monthYear", headerName: "Month/Year" },
  { field: "merchantDBA", headerName: "Merchant DBA" },
  { field: "merchantID", headerName: "Merchant MID" },
  { field: "chargebackItemCount", headerName: "Chargeback Item Count" },
  { field: "chargebackDollarAmount", headerName: "Chargeback Dollar Amount" },
  { field: "retrievalItemCount", headerName: "Retrieval Item Count" },
  { field: "retrievalDollarAmount", headerName: "Retrieval Dollar Amount" },
  { field: "arbitrationItemCount", headerName: "Arbitration Item Count" },
  { field: "arbitrationDollarAmount", headerName: "Arbitration Dollar Amount" },
];

const tableSettingsParams = {
  tableType: AvailableUserSettingsTypes.CHARGEBACK_REPORT_TABLE,
  tableColumns,
};

type RequestParams = Partial<ChargebackReportDataDto> & {
  page?: string;
  size?: string;
  sort?: string;
};

interface FilterSettings extends ChargebackReportDataDto {}

interface TableRow {
  monthYear: string;
  merchantDBA: string;
  merchantMID: string;
  chargebackItemCount: number;
  chargebackDollarAmount: string;
  retrievalItemCount: number;
  retrievalDollarAmount: string;
  arbitrationItemCount: number;
  arbitrationDollarAmount: string;
}

export const ChargebacksReport: React.FC = () => {
  const [filterSettings, setFilterSettings] = useState(default_ChargebackReportFilterSettings);

  const [queryParams, setQueryParams] = useQueryParams({
    bankId: withDefault(NumberParam, null),
    month: withDefault(NumberParam, null),
    year: withDefault(NumberParam, null),
  });

  const [tableData, setTableData] = useState<{
    rows: TableRow[];
    totalAmount: number;
    totalRecords: number;
    isLoading: boolean;
  }>({
    rows: [],
    totalAmount: 0,
    totalRecords: 0,
    isLoading: false,
  });

  const fs = useMemo<FilterSettings>(() => {
    return {
      ...filterSettings,
      ...queryParams,
    };
  }, [filterSettings, queryParams]);

  const dataRequestParams = useMemo(() => {
    return {
      request: (AlertApi.generateChargebackReportData as unknown) as (
        settings: RequestParams
      ) => Promise<AxiosResponse<PaginatedResponse<Record<string, any>[]>>>,
      requestParams: {
        ...fs,
      },
    };
  }, [fs]);

  const [{ isDataLoading, rowsToDisplay, totalAmount, totalRecords }, fetchData] = useFetch({
    dataRequestParams,
    tableSettingsParams,
    skipChecks: true,
  });

  const [isFirstSubmit, setIsFirstSubmit] = useState(false);

  useEffect(() => {
    if (isFirstSubmit) {
      fetchData();
    }
  }, [fetchData, isFirstSubmit]);

  const handleSubmit = useCallback((settings: ChargebackReportFilterSettings) => {
    setIsFirstSubmit(true);
    setFilterSettings(NewObjectUtils.getObjectWithNewReference(settings));
  }, []);

  const handleResetFilter = useCallback(() => {
    setFilterSettings(default_ChargebackReportFilterSettings);
    setQueryParams({
      bankId: null,
      month: null,
      year: null,
    });
  }, [setQueryParams]);

  return (
    <>
      <PageTitle>Chargebacks Report</PageTitle>
      <Breadcrumbs crumbs={breadcrumbs} />

      <ChargebacksReportFilter
        onSubmit={handleSubmit}
        onReset={handleResetFilter}
        values={{
          bankId: filterSettings.bankId || null,
          month: filterSettings.month || null,
          year: filterSettings.year || null,
        }}
      />
      <ActionButtonsContainer>
        <ExportButtonLink
          disabled={!filterSettings.year || !filterSettings.month}
          href={AlertApi.exportChargebackReportData(filterSettings)}
        />
      </ActionButtonsContainer>

      <AdvancedTable
        setColumns={(tableSettings) => tableSettings.configs.columns.map((c: any) => c)}
        tableSettingsParams={tableSettingsParams}
        isLoading={isDataLoading}
        rows={rowsToDisplay}
        totalAmount={totalAmount}
        totalRecords={totalRecords}
      />
    </>
  );
};

export default ChargebacksReport;
