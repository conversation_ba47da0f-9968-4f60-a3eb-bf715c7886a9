import { GridColumns } from "@material-ui/x-grid";

import { AvailableUserSettingsTypes, UserSettings } from "../domain/user-config/api/user-config-api";
import { EFrequency, ERecurringScheduleType, UserRole, UserStatus } from "../swagger-override-types";

export const DEFAULT_COLUMN_WIDTH_PX = 300;

export const getDefaultTableSettings = (type: AvailableUserSettingsTypes, columns: GridColumns): UserSettings => {
  return {
    type,
    configs: {
      columns: columns.map((c) => ({
        width: DEFAULT_COLUMN_WIDTH_PX,
        hide: false,
        ...c,
      })),
      density: "standard",
    },
  };
};

export interface IMonth {
  number: number;
  title: string;
}

export const monthsWithTitles: IMonth[] = [
  { number: 1, title: "January" },
  { number: 2, title: "February" },
  { number: 3, title: "March" },
  { number: 4, title: "April" },
  { number: 5, title: "May" },
  { number: 6, title: "June" },
  { number: 7, title: "July" },
  { number: 8, title: "August" },
  { number: 9, title: "September" },
  { number: 10, title: "October" },
  { number: 11, title: "November" },
  { number: 12, title: "December" },
];

export enum CardTypes {
  VS = "VS",
  MC = "MC",
  DS = "DS",
  AX = "AX",
  ACH = "ACH",
}

export enum ECheckTransactionsApprovalStatuses {
  CHECK_ACCEPTED = "CHECK ACCEPTED",
  DECLINED = "DECLINED",
}

export enum ECheckSettlementApprovalStatuses {
  SETTLED = "SETTLED",
  REJECTED = "REJECTED",
}

export enum CreditCardAuthorizationsApprovalStatuses {
  APPROVED = "APPROVED",
  DECLINED = "DECLINED",
}

export enum CreditCardSettlementApprovalStatuses {
  SETTLED = "SETTLED",
}

export type ApprovalStatusType =
  | ECheckTransactionsApprovalStatuses
  | ECheckSettlementApprovalStatuses
  | CreditCardAuthorizationsApprovalStatuses
  | CreditCardSettlementApprovalStatuses;

export enum TransactionTypes {
  AUTH_ONLY = "AUTHONLY",
  CAPT = "CAPT",
  SALE = "SALE",
  REFUND = "REFUND",
  VOID = "VOID",
  CHECK = "CHECK",
  TOKENIZE = "TOKENIZE",
}

export enum StatusTypes {
  COMPLETED_DEBIT = "COMPLETED DEBIT",
  COMPLETED_CREDIT = "COMPLETED CREDIT",
  REJECT = "REJECT",
}

export enum FundingDispositionTypes {
  BEFORE = "BEFORE",
  AFTER = "AFTER",
}

// ACH_CC - E-check/Credit-card

export interface ACH_CC_FilterSettings {
  fromDate?: string;
  toDate?: string;
  merchantId?: string;
  ccMerchantId?: string;
  achMerchantId?: string;
  cardType?: CardTypes;
  approvalStatus?: ApprovalStatusType;
  terminalId?: string;
  transactionId?: string;
  cardNumber?: string;
  cardHolder?: string;
  authCode?: string;
  ud1?: string;
  ud2?: string;
  ud3?: string;
  orderNumber?: string;
  transactionType?: TransactionTypes;
  amount?: string;
  stType?: string;
  achStType?: string;
  settleToDate?: string;
  settleFromDate?: string;
  funding?: string;
  status?: string;
  activeStatus?: string;
  ipTransactionID?: string;
  reportType?: string;
  authStartDateTime?: string;
  authEndDateTime?: string;
  subType?: string;
  year?: number | null;
  month?: number | null;
}

export const default_ACH_CC_filterSettings: ACH_CC_FilterSettings = {
  fromDate: undefined,
  toDate: undefined,
  merchantId: "",
  cardType: undefined,
  approvalStatus: undefined,
  terminalId: "",
  transactionId: "",
  cardNumber: "",
  cardHolder: "",
  authCode: "",
  ud1: "",
  ud2: "",
  ud3: "",
  orderNumber: "",
  transactionType: undefined,
  funding: undefined,
  status: "",
  amount: "",
  activeStatus: "",
  year: null,
  month: null,
};

// Chargeback-specific filter settings
export interface ChargebackFilterSettings {
  fromDate?: string;
  toDate?: string;
  merchantId?: string;
  merchantName?: string;
  cardType?: CardTypes;
  caseNumber?: string;
  creditCard?: string;
  amount?: string;
  reason?: string;
  resolutionTo?: string;
  debitCredit?: string;
  type?: string;
  originRef?: string;
  arn?: string; // Acquirer Reference Number
}

export const default_ChargebackFilterSettings: ChargebackFilterSettings = {
  fromDate: undefined,
  toDate: undefined,
  merchantId: "",
  merchantName: "",
  cardType: undefined,
  caseNumber: "",
  creditCard: "",
  amount: "",
  reason: "",
  resolutionTo: "",
  debitCredit: "",
  type: "",
  originRef: "",
  arn: "",
};

export enum AMStatuses {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
}

export enum AMProcessors {
  WAB = "WAB",
  MERRICK = "Merrick",
  FRESNO = "Fresno",
  GLOBAL = "Global",
  SPS = "SPS",
  JETPAY = "Jetpay",
  ORBITAL = "Orbital",
  NMI = "NMI",
}

// AM - Admin/Merchants

export interface AM_FilterSettings {
  search?: string;
  processor?: AMProcessors;
  isActive?: boolean;
  partnerId: number | null;
  bankId: number | null;
}

export const default_AM_FilterSettings: AM_FilterSettings = {
  search: "",
  processor: undefined,
  isActive: undefined,
  partnerId: null,
  bankId: null,
};

// AU - Admin/Users

export interface AU_FilterSettings {
  search: string;
  role: UserRole | "";
  status: UserStatus | "";
  merchantId: string;
  partnerId: number | null;
}

export const default_AU_FilterSettings: AU_FilterSettings = {
  search: "",
  role: "",
  status: "",
  merchantId: "",
  partnerId: null,
};

// AP - Admin/Partners

export interface AP_FilterSettings {
  search: string;
}

export const default_AP_FilterSettings: AP_FilterSettings = {
  search: "",
};

// RS - Recurring Scheduler

export interface RS_FilterSettings {
  merchantId: string;
  paymentType: ERecurringScheduleType | string;
  frequency: EFrequency | string;
  cardHolderName: string;
  cardNumberLast4: string;
  ud1: string;
  terminalId: string;
  startDate: string;
  endDate: string;
}

export const default_RS_FilterSettings: RS_FilterSettings = {
  merchantId: "",
  paymentType: "",
  cardHolderName: "",
  startDate: "",
  endDate: "",
  frequency: "",
  cardNumberLast4: "",
  terminalId: "",
  ud1: "",
};
export interface FindWhitelist {
  activeStatus?: string | undefined;
  terminalId?: string | undefined;
  ipAddress?: string | undefined;
}
export const default_FindWhitelist: FindWhitelist = {
  activeStatus: "",
  terminalId: "",
  ipAddress: "",
};

export interface BlockIPAddressLog {
  terminalID: string | undefined;
  ipAddress: string | undefined;
  startDateTime: string | undefined;
  endDateTime: string | undefined;
  transactionType: string | undefined;
}
export const default_BlockIPAddressLog: BlockIPAddressLog = {
  terminalID: "",
  ipAddress: "",
  startDateTime: "",
  endDateTime: "",
  transactionType: "",
};

export interface RemoveTerminalIP {
  terminalID: string | undefined;
  ipAddress: string | undefined;
  startDateTime: string | undefined;
  endDateTime: string | undefined;
  transactionType: string | undefined;
}

export enum GatewayReportTypes {
  TOTAL_CC_DATA = "Total-CC-Data",
  TOTAL_ACH_DATA = "Total-ACH-Data",
}
export interface GatewayReportFilterSettings {
  ipTransactionID: string;
  // authStartDateTime: string;
  // authEndDateTime: string;
  year: number | null | undefined;
  month: number | null | undefined;
  transactionId?: string;
  reportType: GatewayReportTypes;
}
export const default_GatewayReportFilterSettings: GatewayReportFilterSettings = {
  ipTransactionID: "",
  // authStartDateTime: "",
  // authEndDateTime: "",
  year: null,
  month: null,
  transactionId: "",
  reportType: GatewayReportTypes.TOTAL_ACH_DATA,
};
export enum TsysReportTypes {
  MONTHLY_RESIDUAL_DATA = "Monthly Residual Data",
  PPM_MONTHLY_PROFITABILITY_DATA = "PPM Monthly Profitability Data",
}

export interface TsysMonthlyResidualReportFilterSettings {
  merchantId?: string;
  reportType?: TsysReportTypes | string;
  year: number | null | undefined;
  month: number | null | undefined;
}
export const default_TsysMonthlyResidualReportFilterSettings: TsysMonthlyResidualReportFilterSettings = {
  merchantId: "",
  reportType: TsysReportTypes.MONTHLY_RESIDUAL_DATA,
  year: null,
  month: null,
};

export interface ChargebackReportFilterSettings {
  bankId: number | null | undefined;
  month: number | null | undefined;
  year: number | null | undefined;
}

export const default_ChargebackReportFilterSettings: ChargebackReportFilterSettings = {
  bankId: null,
  month: null,
  year: null,
};

export interface ChargebackReportFilterSettings {
  bankId: number | null | undefined;
  month: number | null | undefined;
  year: number | null | undefined;
}
