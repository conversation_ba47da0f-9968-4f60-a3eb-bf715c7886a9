import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import { Button, FormControl, Grid, InputLabel, MenuItem, InputAdornment } from "@material-ui/core";
import SearchIcon from "@material-ui/icons/Search";
import { DatePicker, DatePickerView } from "@material-ui/pickers";
import { MaterialUiPickersDate } from "@material-ui/pickers/typings/date";

import {
  ACH_CC_FilterSettings,
  default_ACH_CC_filterSettings,
  default_GatewayReportFilterSettings,
  GatewayReportFilterSettings,
  GatewayReportTypes,
} from "../../../common/entities";
import { monthsWithTitles } from "../../../common/entities";
import { getLast30Years } from "../../../utils/dateString";
import { LoadingButton } from "../../../components/buttons/LoadingButton";
import { CustomTextField } from "../../../components/fields/CustomTextField";
import { StyledSelect } from "../../../components/selects/StyledSelect";
import { useFilterQueryParams } from "../../../hooks/useFilterQueryParams";
import { DATE_ONLY_FORMAT, getApiSuitableDateString, getPickerDateString } from "../../../utils/dateString";

import { FilterTitle, FilterContainer } from "../../../styles/Global.styles";
import { Autocomplete } from "@material-ui/lab";
import styles from "../../../domain/merchant-statement/components/MerchantStatementsFilter.module.scss";

const datePickerViews: DatePickerView[] = ["year", "month", "date"];
export interface GatewayReportFilterProps {
  onSubmit: (settings: GatewayReportFilterSettings) => void;
  onReset: () => void;
  values: GatewayReportFilterSettings;
  isSearching?: boolean;
}
export const GatewayReportFilter: React.FC<GatewayReportFilterProps> = ({ onSubmit, onReset, values, isSearching }) => {
  const [isDFDatepickerOpen, setDFDatepickerOpenState] = useState(false);
  const [isDTDatepickerOpen, setDTDatepickerOpenState] = useState(false);
  const [filterSettings, setFilterSettings] = useState(default_GatewayReportFilterSettings);
  const [isLoading, setLoadingState] = useState(false);

  const { filterQueryParams, setFilterQueryParams, resetAllFilterQueryParams } = useFilterQueryParams();

  const changeFilterSettings = useCallback(
    (field: keyof ACH_CC_FilterSettings, value: any) => {
      setFilterSettings((prevSettings) => ({
        ...prevSettings,
        [field]: value,
      }));
    },
    [setFilterSettings]
  );

  const handleSubmit = useCallback(() => {
    setFilterQueryParams(
      {
        ...filterSettings,
        settleToDate: filterQueryParams.settleToDate,
        settleFromDate: filterQueryParams.settleFromDate,
      },
      "replace"
    );
    return onSubmit(filterSettings);
  }, [onSubmit, filterSettings, setFilterQueryParams, filterQueryParams]);

  const handleReset = useCallback(() => {
    onReset();
    setFilterSettings(default_GatewayReportFilterSettings);
  }, [onReset]);

  const renderLabel = useCallback((d: MaterialUiPickersDate, invalidLabel: string) => {
    if (!d) {
      return "Select Date";
    }
    return getApiSuitableDateString(d, DATE_ONLY_FORMAT);
  }, []);

  // const pickerDates = useMemo(() => {
  //   const asUTC = (date?: string) => (date ? getPickerDateString(date) : null);
  //   const authStartDateTime = asUTC(filterSettings.authStartDateTime);
  //   const authEndDateTime = asUTC(filterSettings.authEndDateTime);
  //   return {
  //     authStartDateTime,
  //     authEndDateTime,
  //   };
  // }, [filterSettings]);
  const loading = isLoading || isSearching;

  return (
    <FilterContainer>
      <FilterTitle>Filter</FilterTitle>
      <Grid container spacing={6} alignItems="flex-end">
        <Grid item lg={3} md={4} xs={12}>
          <CustomTextField
            fullWidth
            label="IP Transaction ID"
            placeholder="IP Transaction ID"
            value={filterSettings.ipTransactionID}
            onChange={(e) => changeFilterSettings("ipTransactionID", e.target.value)}
            disabled={isSearching}
          />
        </Grid>
        <Grid item lg={3} md={4} xs={12}>
          <CustomTextField
            fullWidth
            label="Transaction ID"
            placeholder="Transaction ID"
            value={filterSettings.transactionId}
            onChange={(e) => changeFilterSettings("transactionId", e.target.value)}
            disabled={isSearching}
          />
        </Grid>
        <Grid item lg={3} md={4} xs={12}>
          <FormControl fullWidth>
            <InputLabel>Report Type</InputLabel>
            <StyledSelect
              value={filterSettings.reportType || ""}
              onChange={(e) => changeFilterSettings("reportType", e.target.value as GatewayReportTypes)}
              disabled={isSearching}
            >
              <MenuItem value={GatewayReportTypes.TOTAL_CC_DATA}>Total-CC-Data</MenuItem>
              <MenuItem value={GatewayReportTypes.TOTAL_ACH_DATA}>Total-ACH-Data</MenuItem>
            </StyledSelect>
          </FormControl>
        </Grid>
        <Grid item lg={2} md={4} xs={12}>
          <Autocomplete
            options={getLast30Years()}
            renderInput={(params) => <CustomTextField {...params} variant="standard" label="Year" />}
            onChange={(e, v) => changeFilterSettings("year", v)}
            value={filterSettings.year}
            getOptionLabel={(o) => o.toString()}
            classes={{
              popper: styles.popover,
            }}
            disabled={loading}
          />
        </Grid>
        <Grid item lg={2} md={4} xs={12}>
          <FormControl fullWidth disabled={loading}>
            <InputLabel>Month</InputLabel>
            <StyledSelect
              onChange={(e) => changeFilterSettings("month", Number(e.target.value))}
              displayEmpty
              value={filterSettings.month ?? ""}
            >
              {monthsWithTitles.map((m) => (
                <MenuItem key={m.number} value={m.number}>
                  {m.title}
                </MenuItem>
              ))}
            </StyledSelect>
          </FormControl>
        </Grid>
        {/* <Grid item lg={2} md={4} xs={12}>
          <DatePicker
            label="Start Date"
            onChange={(date) => {
              const formattedDate = getApiSuitableDateString(date);
              changeFilterSettings("authStartDateTime", formattedDate);
            }}
            value={pickerDates.authStartDateTime}
            labelFunc={renderLabel}
            variant="inline"
            views={datePickerViews}
            autoOk
            fullWidth
            disabled={isSearching}
            open={isDFDatepickerOpen}
            onOpen={() => setDFDatepickerOpenState(true)}
            onClose={() => setDFDatepickerOpenState(false)}
            PopoverProps={{
              TransitionProps: { appear: true, exit: false, enter: false },
            }}
            maxDate={pickerDates.authEndDateTime ?? undefined}
          />
        </Grid>
        <Grid item lg={2} md={4} xs={12}>
          <DatePicker
            label="End Date"
            fullWidth
            value={pickerDates.authEndDateTime}
            onChange={(date) => {
              const formattedDate = getApiSuitableDateString(date);
              changeFilterSettings("authEndDateTime", formattedDate);
            }}
            labelFunc={renderLabel}
            variant="inline"
            views={datePickerViews}
            open={isDTDatepickerOpen}
            autoOk
            disabled={isSearching}
            onOpen={() => setDTDatepickerOpenState(true)}
            onClose={() => setDTDatepickerOpenState(false)}
            PopoverProps={{
              TransitionProps: { appear: true, exit: false, enter: false },
            }}
            minDate={pickerDates.authStartDateTime ?? undefined}
          />
        </Grid> */}
        {/* {showQuickJumpDateDropdown && (
          <Grid item lg={2} md={4} xs={12}>
            <QuickJumpDateDropdown
              merchantId={filterSettings.merchantId as string}
              disabled={isSearching || (disableToolbar && !filterSettings.merchantId)}
              datesRequest={quickJumpDateRequest}
              onChange={(date) => {
                if (date) {
                  changeFilterSettings("authStartDateTime", getApiSuitableDateString(date));
                  changeFilterSettings("aut", getApiSuitableDateString(date));
                } else {
                  changeFilterSettings("authStartDateTime", undefined);
                  changeFilterSettings("aut", undefined);
                }
              }}
              selectedDate={
                filterSettings.authStartDateTime &&
                filterSettings.aut &&
                filterSettings.authStartDateTime === filterSettings.aut
                  ? getApiSuitableDateString(filterSettings.authStartDateTime, DATE_ONLY_FORMAT)
                  : ""
              }
            />
          </Grid>
        )} */}
        <Grid item lg={1} md={6} xs={12}>
          <LoadingButton
            variant="contained"
            fullWidth
            color="primary"
            onClick={handleSubmit}
            isLoading={isSearching}
            disabled={filterSettings.year === null || filterSettings.month === null}
          >
            Search
          </LoadingButton>
        </Grid>
        <Grid item lg={1} md={6} xs={12}>
          <Button
            variant="contained"
            onClick={handleReset}
            fullWidth
            color="primary"
            disabled={isSearching || (filterSettings.year === null && filterSettings.month === null)}
          >
            Reset
          </Button>
        </Grid>
      </Grid>
    </FilterContainer>
  );
};
