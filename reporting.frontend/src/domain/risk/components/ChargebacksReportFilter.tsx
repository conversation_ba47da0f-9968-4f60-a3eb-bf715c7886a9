import React, { use<PERSON><PERSON>back, useMemo, useState, useEffect } from "react";

import { Button, FormControl, Grid, InputLabel, MenuItem, TextField } from "@material-ui/core";
import { Autocomplete } from "@material-ui/lab";
import { DatePicker, DatePickerView } from "@material-ui/pickers";
import { MaterialUiPickersDate } from "@material-ui/pickers/typings/date";
import styled from "styled-components";
import { NumberParam, StringParam, useQueryParams } from "use-query-params";

import { LoadingButton } from "../../../components/buttons/LoadingButton";
import { CustomTextField } from "../../../components/fields/CustomTextField";
import { DATE_ONLY_FORMAT, getApiSuitableDateString, getPickerDateString } from "../../../utils/dateString";
// import { TerminalIpsBlocking, WhitelistApi } from "../api/whitelist-api";

import { getLast30Years } from "../../../utils/dateString";

import { FilterContainer, FilterTitle } from "../../../styles/Global.styles";
import { default_ChargebackReportFilterSettings, monthsWithTitles } from "../../../common/entities";
import { BanksAutocomplete } from "../../bank/components/BankAutocomplete";
import { useFilterQueryParams } from "../../../hooks/useFilterQueryParams";
import { StyledSelect } from "../../../components/selects/StyledSelect";

export interface TransactionFilterProps {
  onSubmit: (settings: {
    bankId: number | null | undefined;
    month: number | null | undefined;
    year: number | null | undefined;
  }) => void;
  onReset: () => void;
  fullWidth?: boolean;
  disableToolbar?: boolean;
  hideFilterButton?: boolean;
  isSearching?: boolean;
  values: {
    bankId: number | null | undefined;
    month: number | null | undefined;
    year: number | null | undefined;
  };
}

export const ChargebacksReportFilter: React.FC<TransactionFilterProps> = ({
  onSubmit,
  onReset,
  fullWidth,
  disableToolbar,
  hideFilterButton,
  isSearching,
  values,
}) => {
  const [, setQueryParams] = useQueryParams({
    bankId: NumberParam,
    month: NumberParam,
    year: NumberParam,
  });

  const [filterSettings, setFilterSettings] = useState(default_ChargebackReportFilterSettings);

  const { filterQueryParams, setFilterQueryParams, resetAllFilterQueryParams } = useFilterQueryParams();

  const changeFilterSettings = useCallback(
    (field: keyof typeof filterSettings, value: any) => {
      setFilterSettings((settings) => ({ ...settings, [field]: value }));
    },
    [setFilterSettings]
  );

  const handleSubmit = useCallback(() => {
    const updatedSettings = {
      ...filterSettings,
    };
    setQueryParams(
      {
        bankId: filterSettings.bankId,
        month: filterSettings.month,
        year: filterSettings.year,
      },
      "replace"
    );
    return onSubmit(updatedSettings);
  }, [onSubmit, filterSettings, setQueryParams]);

  const handleReset = useCallback(() => {
    setFilterSettings(default_ChargebackReportFilterSettings);
    setQueryParams(
      {
        bankId: null,
        year: null,
        month: null,
      },
      "replace"
    );

    onReset();
  }, [onReset, setQueryParams]);

  const renderLabel = useCallback((d: MaterialUiPickersDate, invalidLabel: string) => {
    if (!d) {
      return "Select Date";
    }
    return getApiSuitableDateString(d, DATE_ONLY_FORMAT);
  }, []);
  const isFilterEmpty = useMemo(() => {
    return Object.values(filterSettings).every((value) => !value);
  }, [filterSettings]);

  return (
    <FilterContainer>
      <FilterTitle>Filter</FilterTitle>
      <Grid container spacing={6} alignItems={"flex-end"}>
        <Grid item lg={2} md={6} xs={12}>
          <FormControl fullWidth>
            <BanksAutocomplete
              bankId={filterSettings.bankId ?? null}
              setBankId={(id) => changeFilterSettings("bankId", id)}
              disabled={isSearching}
            />
          </FormControl>
        </Grid>
        <Grid item lg={2} md={6} xs={12}>
          <Autocomplete
            options={getLast30Years()}
            renderInput={(params) => <CustomTextField {...params} variant={"standard"} label={"Year"} />}
            onChange={(e, v) => changeFilterSettings("year", v)}
            value={filterSettings.year ?? null}
            getOptionLabel={(o) => o.toString()}
            // classes={{
            //   popper: styles.popover,
            // }}
            // disabled={loading}
          />
        </Grid>

        <Grid item lg={2} md={6} xs={12}>
          <FormControl fullWidth>
            <InputLabel>Month</InputLabel>
            <StyledSelect
              onChange={(e) => changeFilterSettings("month", e.target.value)}
              displayEmpty
              // disabled={loading}
              fullWidth
              value={filterSettings.month}
            >
              {monthsWithTitles.map((m) => (
                <MenuItem key={m.number} value={m.number}>
                  {m.title}
                </MenuItem>
              ))}
            </StyledSelect>
          </FormControl>
        </Grid>

        <Grid item lg={1} md={6} xs={12}>
          <LoadingButton
            variant={"contained"}
            color={"primary"}
            fullWidth
            disabled={
              disableToolbar ||
              isFilterEmpty ||
              isSearching ||
              !filterSettings.year ||
              !filterSettings.month ||
              !filterSettings.bankId // Ensure both year and month are filled
            }
            onClick={handleSubmit}
            isLoading={isSearching}
          >
            Search
          </LoadingButton>
        </Grid>
        <Grid item lg={1} md={6} xs={12}>
          <Button
            fullWidth
            disabled={disableToolbar || isSearching}
            variant={"contained"}
            onClick={handleReset}
            color={"primary"}
          >
            Reset
          </Button>
        </Grid>
      </Grid>
    </FilterContainer>
  );
};
