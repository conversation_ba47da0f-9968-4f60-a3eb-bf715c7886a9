import { axiosPrivateInstance } from "../../../api/axios-config";
import { AxiosResponse } from "axios";
import { PaginatedResponse } from "../../../api/types";
import { typedEnv } from "../../../environments/environment";
import { createQueryString } from "../../../utils/createQueryString";

export enum AlertType {
  MONTHLY_SETTLEMENT = "MONTHLY_SETTLEMENT",
  DAILY_AUTH_COUNT = "DAILY_AUTH_COUNT",
  VOIDS = "VOIDS",
  CHARGEBACK = "CHARGEBACK",
  DECLINE_APPROVAL_RATIO = "DECLINE_APPROVAL_RATIO",
  FIRST_CHARGEBACK = "FIRST_CHARGEBACK",
}
export interface updateThresholdSettingDto {
  alertType: AlertType;
  merchantId: string;
  percentAmountLimit: number;
  countLimit: number;
  triggerAlertOne: number;
  triggerAlertTwo: number;
  triggerAlertThree: number;
  alertToSupport: boolean;
  alertToRisk: boolean;
  alertToMerchant: boolean;
  alertToPartner: boolean;
  triggerAlertTime: string;
}
export interface ThresholdSettingDto {
  alertType: string;
  merchantId: string;
}

export interface ChargebackReportDataDto {
  bankId: number | null | undefined;
  year: number | null | undefined;
  month: number | null | undefined;
}

export class AlertApi {
  static updateThresholdSetting(data: updateThresholdSettingDto) {
    return axiosPrivateInstance.put(`/threshold-setting/`, data);
  }
  static getThresholdSetting(params: ThresholdSettingDto) {
    return axiosPrivateInstance.get(`/threshold-setting/`, { params });
  }
  static generateChargebackReportData(params: ChargebackReportDataDto) {
    return axiosPrivateInstance.get(`/generate-chargeback-report-data`, { params });
  }
  static exportChargebackReportData(params: ChargebackReportDataDto) {
    return `${typedEnv.REACT_APP_API_URL}/private/generate-chargeback-report-data/export${createQueryString(params)}`;
  }
}
