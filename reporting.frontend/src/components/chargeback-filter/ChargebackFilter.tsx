import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";

import { Button, Grid, IconButton } from "@material-ui/core";
import { FilterList as FilterIcon } from "@material-ui/icons";
import { DatePicker, DatePickerView } from "@material-ui/pickers";
import { MaterialUiPickersDate } from "@material-ui/pickers/typings/date";
import { AxiosResponse } from "axios";
import styled from "styled-components";

import { ChargebackFilterSettings, default_ChargebackFilterSettings } from "../../common/entities";
import { useFilterQueryParams } from "../../hooks/useFilterQueryParams";
import { MerchantOutDto } from "../../swagger-override-types";
import { EFilterType } from "../../types/filter.types";
import { DATE_ONLY_FORMAT, getApiSuitableDateString, getPickerDateString } from "../../utils/dateString";
import { MerchantsAutocomplete } from "../autocompletes/MerchantsAutocomplete";
import { LoadingButton } from "../buttons/LoadingButton";
import { QuickJumpDateDropdown } from "../dropdowns/QuickJumpDateDropdown";
import { ChargebackDrawerFilter } from "../transaction-filter/chargeback-drawer-filter/ChargebackDrawerFilter";

import { FilterContainer, FilterTitle } from "../../styles/Global.styles";

export interface ChargebackFilterProps {
  onSubmit: (settings: ChargebackFilterSettings, filterType: EFilterType) => void;
  onResetFilter: () => void;
  onResetDrawerFilter: () => void;
  hideQuickJumpDate?: boolean;
  setMerchant?: (merchant: MerchantOutDto | null) => void;
  setDateFrom?: (fromDate: any) => void;
  setDateTo?: (toDate: any) => void;
  fullWidth?: boolean;
  disableToolbar?: boolean;
  hideFilterButton?: boolean;
  optionalMerchantId?: boolean;
  quickJumpDateRequest?: (merchantId: string) => Promise<AxiosResponse<string[]>> | undefined;
  isSearching?: boolean;
}

interface IFilterInnerIconButton {
  disabled?: boolean;
}

const FilterInnerIconButton = styled(IconButton)<IFilterInnerIconButton>`
  position: absolute;
  right: 8px;
  top: 5px;
  opacity: ${({ disabled }) => (disabled ? 0.5 : 1)};
  pointer-events: ${({ disabled }) => (disabled ? "none" : "auto")};
`;

export const ChargebackFilter: React.FC<ChargebackFilterProps> = ({
  onSubmit,
  onResetDrawerFilter,
  onResetFilter,
  setMerchant,
  setDateFrom,
  setDateTo,
  hideQuickJumpDate,
  fullWidth,
  disableToolbar,
  hideFilterButton,
  optionalMerchantId,
  quickJumpDateRequest,
  isSearching,
}) => {
  const [isDrawerOpen, setDrawerVisibilityState] = useState(false);
  const [filterSettings, setFilterSettings] = useState(default_ChargebackFilterSettings);

  const {
    filterQueryParams,
    setFilterQueryParams,
    resetAllFilterQueryParams,
    resetAllAdditionalFilterQueryParams,
  } = useFilterQueryParams();

  useEffect(() => {
    setFilterSettings(filterQueryParams as ChargebackFilterSettings);
  }, [setFilterSettings, filterQueryParams]);

  const toggleDrawerVisibilityState = useCallback(() => {
    setDrawerVisibilityState((prev) => !prev);
  }, []);

  const changeFilterSettings = useCallback(
    (field: keyof ChargebackFilterSettings, value: any) => {
      const newFilterSettings = { ...filterSettings, [field]: value };
      setFilterSettings(newFilterSettings);
      setFilterQueryParams({ [field]: value }, "replaceIn");
    },
    [filterSettings, setFilterQueryParams]
  );

  const handleSubmit = useCallback(() => {
    onSubmit(filterSettings, EFilterType.SEARCH);
  }, [filterSettings, onSubmit]);

  const handleResetDrawerFilter = useCallback(() => {
    resetAllAdditionalFilterQueryParams();
    onResetDrawerFilter();
  }, [resetAllAdditionalFilterQueryParams, onResetDrawerFilter]);

  const handleResetFilter = useCallback(() => {
    resetAllFilterQueryParams();
    onResetFilter();
  }, [resetAllFilterQueryParams, onResetFilter]);

  const onMerchantChange = useCallback(
    (m: MerchantOutDto | null, setInQuery?: boolean) => {
      setMerchant?.(m);
      changeFilterSettings("merchantId", m?.merchantId);

      if (setInQuery) {
        setFilterQueryParams(
          {
            merchantId: m?.merchantId,
          },
          "replaceIn"
        );
      }
    },
    [changeFilterSettings, setMerchant, setFilterQueryParams]
  );

  const pickerDates = useMemo(() => {
    const asUTC = (date?: string) => (date ? getPickerDateString(date) : null);
    const fromDate = asUTC(filterSettings.fromDate);
    const toDate = asUTC(filterSettings.toDate);
    return {
      fromDate,
      toDate,
    };
  }, [filterSettings]);

  const showQuickJumpDateDropdown = !hideQuickJumpDate && quickJumpDateRequest;

  return (
    <FilterContainer>
      <FilterTitle>Filter</FilterTitle>
      {hideFilterButton ? null : (
        <FilterInnerIconButton
          disabled={
            !(filterSettings.merchantId || optionalMerchantId) ||
            !filterSettings.fromDate ||
            !filterSettings.toDate ||
            isSearching
          }
          onClick={toggleDrawerVisibilityState}
        >
          <FilterIcon />
        </FilterInnerIconButton>
      )}
      <Grid container alignItems={"flex-end"} spacing={6}>
        <Grid item lg={2} md={6} xs={12}>
          <MerchantsAutocomplete
            merchantId={filterSettings.merchantId}
            setMerchantId={(m) => onMerchantChange(m, true)}
            disabled={isSearching}
            required={!optionalMerchantId}
          />
        </Grid>
        <Grid item lg={2} md={6} xs={12}>
          <DatePicker
            label="Date From"
            value={pickerDates.fromDate}
            onChange={(date: MaterialUiPickersDate) => {
              const dateString = getApiSuitableDateString(date);
              changeFilterSettings("fromDate", dateString);
              setDateFrom?.(date);
            }}
            format={DATE_ONLY_FORMAT}
            views={["year", "month", "date"] as DatePickerView[]}
            disabled={isSearching}
            required
          />
        </Grid>
        <Grid item lg={2} md={6} xs={12}>
          <DatePicker
            label="Date To"
            value={pickerDates.toDate}
            onChange={(date: MaterialUiPickersDate) => {
              const dateString = getApiSuitableDateString(date);
              changeFilterSettings("toDate", dateString);
              setDateTo?.(date);
            }}
            format={DATE_ONLY_FORMAT}
            views={["year", "month", "date"] as DatePickerView[]}
            disabled={isSearching}
            required
          />
        </Grid>
        {showQuickJumpDateDropdown && (
          <Grid item lg={2} md={6} xs={12}>
            <QuickJumpDateDropdown
              merchantId={filterSettings.merchantId}
              quickJumpDateRequest={quickJumpDateRequest}
              onDateChange={(fromDate, toDate) => {
                changeFilterSettings("fromDate", fromDate);
                changeFilterSettings("toDate", toDate);
              }}
              disabled={!filterSettings.merchantId || isSearching}
            />
          </Grid>
        )}
        <Grid item lg={fullWidth ? 12 : 2} md={6} xs={12}>
          <LoadingButton
            variant="contained"
            color="primary"
            onClick={handleSubmit}
            disabled={
              !(filterSettings.merchantId || optionalMerchantId) ||
              !filterSettings.fromDate ||
              !filterSettings.toDate ||
              isSearching
            }
            loading={isSearching}
          >
            Search
          </LoadingButton>
        </Grid>
        <Grid item lg={2} md={6} xs={12}>
          <Button variant="outlined" onClick={handleResetFilter} disabled={isSearching}>
            Reset
          </Button>
        </Grid>
      </Grid>
      <ChargebackDrawerFilter
        open={isDrawerOpen}
        onClose={toggleDrawerVisibilityState}
        filterSettings={filterSettings}
        onSubmit={handleSubmit}
        onReset={handleResetDrawerFilter}
        changeFilterSettings={changeFilterSettings}
        isSearching={isSearching}
      />
    </FilterContainer>
  );
};
