import React from "react";

import { <PERSON><PERSON>, Drawer, Typography } from "@material-ui/core";
import CloseIcon from "@material-ui/icons/Close";

import { ACH_CC_FilterSettings } from "../../../common/entities";
import { ECheckRouteNames } from "../../../routes/constants";
import { CreditCard4Mask } from "../../CreditCardTextMask";
import { CustomTextField } from "../../fields/CustomTextField";
import { TransactionFilterProps } from "../TransactionFilter";
import { ApprovalStatusFilter } from "./components/ApprovalStatusFilter";
import { CardTypeFilter } from "./components/CardTypeFilter";
import { FundingDispositionFilter } from "./components/FundingDispositionFilter";
import { StatusFilter } from "./components/StatusFilter";
import { TransactionTypeFilter } from "./components/TransactionTypeFilter";

import { DrawerFilterStyles } from "../../../styles/DrawerFilter.styles";

interface Props {
  open: boolean;
  onClose: () => void;
  filterSettings: ACH_CC_FilterSettings;
  onSubmit: () => void;
  onReset: () => void;
  changeFilterSettings: (field: keyof ACH_CC_FilterSettings, value: any) => void;
  isSearching?: boolean;
  type: TransactionFilterProps["type"];
}

export const TransactionDrawerFilter: React.FC<Props> = ({
  open,
  onClose,
  isSearching,
  onSubmit,
  onReset,
  filterSettings,
  changeFilterSettings,
  type,
}) => {
  return (
    <Drawer onClose={onClose} anchor={"right"} open={open}>
      <DrawerFilterStyles.ContentWrapper>
        <DrawerFilterStyles.Header>
          <DrawerFilterStyles.Title variant="h5">Filters</DrawerFilterStyles.Title>
          <DrawerFilterStyles.IconButton onClick={onClose}>
            <CloseIcon />
          </DrawerFilterStyles.IconButton>
        </DrawerFilterStyles.Header>
        <DrawerFilterStyles.ContentScrollable>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>Terminal ID</Typography>
            <CustomTextField
              value={filterSettings.terminalId ?? ""}
              onChange={(e) => changeFilterSettings("terminalId", e.target.value)}
              placeholder={"Terminal ID"}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>Transaction ID</Typography>
            <CustomTextField
              value={filterSettings.transactionId ?? ""}
              onChange={(e) => changeFilterSettings("transactionId", e.target.value)}
              placeholder={"Transaction ID"}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          <CardTypeFilter type={type} changeFilterSettings={changeFilterSettings} cardType={filterSettings.cardType} />
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>
              {type === ECheckRouteNames.SETTLEMENT || type === ECheckRouteNames.TRANSACTIONS
                ? "Account Number"
                : "Card Number"}
            </Typography>
            <CustomTextField
              value={filterSettings.cardNumber ?? ""}
              onChange={(e) => changeFilterSettings("cardNumber", e.target.value)}
              placeholder={`${
                type === ECheckRouteNames.SETTLEMENT || type === ECheckRouteNames.TRANSACTIONS
                  ? "Account Number"
                  : "Card Number"
              } (4 last digits)`}
              variant={"outlined"}
              InputProps={{
                inputComponent: CreditCard4Mask as any,
              }}
            />
          </DrawerFilterStyles.Container>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>
              {type === ECheckRouteNames.SETTLEMENT || type === ECheckRouteNames.TRANSACTIONS
                ? "Account Holder"
                : "Cardholder"}
            </Typography>
            <CustomTextField
              value={filterSettings.cardHolder ?? ""}
              onChange={(e) => changeFilterSettings("cardHolder", e.target.value)}
              placeholder={`${
                type === ECheckRouteNames.SETTLEMENT || type === ECheckRouteNames.TRANSACTIONS
                  ? "Account Holder"
                  : "Cardholder"
              }`}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          {!type?.includes("/e-check/settlement") ? (
            <DrawerFilterStyles.Container>
              <Typography variant={"body1"}>Auth Code</Typography>
              <CustomTextField
                value={filterSettings.authCode ?? ""}
                onChange={(e) => changeFilterSettings("authCode", e.target.value)}
                placeholder={"Auth Code"}
                variant={"outlined"}
              />
            </DrawerFilterStyles.Container>
          ) : null}
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>UD1</Typography>
            <CustomTextField
              value={filterSettings.ud1 ?? ""}
              onChange={(e) => changeFilterSettings("ud1", e.target.value)}
              placeholder={"UD1"}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>UD2</Typography>
            <CustomTextField
              value={filterSettings.ud2 ?? ""}
              onChange={(e) => changeFilterSettings("ud2", e.target.value)}
              placeholder={"UD2"}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>UD3</Typography>
            <CustomTextField
              value={filterSettings.ud3 ?? ""}
              onChange={(e) => changeFilterSettings("ud3", e.target.value)}
              placeholder={"UD3"}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>Order Number</Typography>
            <CustomTextField
              value={filterSettings.orderNumber ?? ""}
              onChange={(e) => changeFilterSettings("orderNumber", e.target.value)}
              placeholder={"Order Number"}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>Amount</Typography>
            <CustomTextField
              value={filterSettings.amount ?? ""}
              onChange={(e) => changeFilterSettings("amount", e.target.value)}
              placeholder={"Amount"}
              InputLabelProps={{
                style: {
                  color: "red",
                },
              }}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          <TransactionTypeFilter
            type={type}
            transactionType={filterSettings.transactionType}
            changeFilterSettings={changeFilterSettings}
          />
          <ApprovalStatusFilter
            type={type}
            approvalStatus={filterSettings.approvalStatus}
            changeFilterSettings={changeFilterSettings}
          />
          <StatusFilter type={type} status={filterSettings.status} changeFilterSettings={changeFilterSettings} />
          <FundingDispositionFilter
            type={type}
            funding={filterSettings.funding}
            changeFilterSettings={changeFilterSettings}
          />
        </DrawerFilterStyles.ContentScrollable>
        <DrawerFilterStyles.ButtonsContainer>
          <Button variant={"contained"} onClick={onSubmit} color={"primary"} disabled={isSearching}>
            Search
          </Button>
          <Button variant={"contained"} color={"primary"} onClick={onReset} disabled={isSearching}>
            Reset
          </Button>
        </DrawerFilterStyles.ButtonsContainer>
      </DrawerFilterStyles.ContentWrapper>
    </Drawer>
  );
};
