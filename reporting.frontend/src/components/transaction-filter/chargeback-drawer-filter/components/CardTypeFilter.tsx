import React from "react";

import { FormControl, MenuItem, Typography } from "@material-ui/core";

import { ChargebackFilterSettings, CardTypes } from "../../../../common/entities";
import { StyledSelect } from "../../../selects/StyledSelect";

import { DrawerFilterStyles } from "../../../../styles/DrawerFilter.styles";

interface Props {
  cardType: ChargebackFilterSettings["cardType"];
  changeFilterSettings: (field: keyof ChargebackFilterSettings, value: any) => void;
}

export const CardTypeFilter: React.FC<Props> = ({ cardType, changeFilterSettings }) => {
  const cardTypeValues = Object.values(CardTypes).filter(type => type !== CardTypes.ACH); // Exclude ACH for chargebacks

  return (
    <DrawerFilterStyles.Container>
      <Typography variant={"body1"}>Card Type</Typography>
      <FormControl>
        <StyledSelect
          variant={"outlined"}
          displayEmpty
          value={cardType ?? ""}
          onChange={(e) => changeFilterSettings("cardType", e.target.value)}
        >
          <MenuItem value={""}>No card type selected</MenuItem>
          {cardTypeValues.map((type) => (
            <MenuItem value={type} key={type}>
              {type}
            </MenuItem>
          ))}
        </StyledSelect>
      </FormControl>
    </DrawerFilterStyles.Container>
  );
};
