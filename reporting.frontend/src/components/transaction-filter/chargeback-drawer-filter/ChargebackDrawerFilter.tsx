import React from "react";

import { <PERSON><PERSON>, Drawer, Typography } from "@material-ui/core";
import CloseIcon from "@material-ui/icons/Close";

import { ChargebackFilterSettings, CardTypes } from "../../../common/entities";
import { CreditCard4Mask } from "../../CreditCardTextMask";
import { CustomTextField } from "../../fields/CustomTextField";
import { CardTypeFilter } from "./components/CardTypeFilter";

import { DrawerFilterStyles } from "../../../styles/DrawerFilter.styles";

interface Props {
  open: boolean;
  onClose: () => void;
  filterSettings: ChargebackFilterSettings;
  onSubmit: () => void;
  onReset: () => void;
  changeFilterSettings: (field: keyof ChargebackFilterSettings, value: any) => void;
  isSearching?: boolean;
}

export const ChargebackDrawerFilter: React.FC<Props> = ({
  open,
  onClose,
  isSearching,
  onSubmit,
  onReset,
  filterSettings,
  changeFilterSettings,
}) => {
  return (
    <Drawer onClose={onClose} anchor={"right"} open={open}>
      <DrawerFilterStyles.ContentWrapper>
        <DrawerFilterStyles.Header>
          <DrawerFilterStyles.Title variant="h5">Filters</DrawerFilterStyles.Title>
          <DrawerFilterStyles.IconButton onClick={onClose}>
            <CloseIcon />
          </DrawerFilterStyles.IconButton>
        </DrawerFilterStyles.Header>
        <DrawerFilterStyles.ContentScrollable>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>Merchant Name</Typography>
            <CustomTextField
              value={filterSettings.merchantName ?? ""}
              onChange={(e) => changeFilterSettings("merchantName", e.target.value)}
              placeholder={"Merchant Name"}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          <CardTypeFilter cardType={filterSettings.cardType} changeFilterSettings={changeFilterSettings} />
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>Case Number</Typography>
            <CustomTextField
              value={filterSettings.caseNumber ?? ""}
              onChange={(e) => changeFilterSettings("caseNumber", e.target.value)}
              placeholder={"Case Number"}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>Credit Card</Typography>
            <CustomTextField
              value={filterSettings.creditCard ?? ""}
              onChange={(e) => changeFilterSettings("creditCard", e.target.value)}
              placeholder={"Credit Card (4 last digits)"}
              variant={"outlined"}
              InputProps={{
                inputComponent: CreditCard4Mask as any,
              }}
            />
          </DrawerFilterStyles.Container>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>Amount</Typography>
            <CustomTextField
              value={filterSettings.amount ?? ""}
              onChange={(e) => changeFilterSettings("amount", e.target.value)}
              placeholder={"Amount"}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>Reason</Typography>
            <CustomTextField
              value={filterSettings.reason ?? ""}
              onChange={(e) => changeFilterSettings("reason", e.target.value)}
              placeholder={"Reason"}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>Resolution To</Typography>
            <CustomTextField
              value={filterSettings.resolutionTo ?? ""}
              onChange={(e) => changeFilterSettings("resolutionTo", e.target.value)}
              placeholder={"Resolution To"}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>Debit/Credit</Typography>
            <CustomTextField
              value={filterSettings.debitCredit ?? ""}
              onChange={(e) => changeFilterSettings("debitCredit", e.target.value)}
              placeholder={"Debit/Credit"}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>Type</Typography>
            <CustomTextField
              value={filterSettings.type ?? ""}
              onChange={(e) => changeFilterSettings("type", e.target.value)}
              placeholder={"Type"}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>Origin Ref</Typography>
            <CustomTextField
              value={filterSettings.originRef ?? ""}
              onChange={(e) => changeFilterSettings("originRef", e.target.value)}
              placeholder={"Origin Ref"}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
          <DrawerFilterStyles.Container>
            <Typography variant={"body1"}>ARN</Typography>
            <CustomTextField
              value={filterSettings.arn ?? ""}
              onChange={(e) => changeFilterSettings("arn", e.target.value)}
              placeholder={"Acquirer Reference Number"}
              variant={"outlined"}
            />
          </DrawerFilterStyles.Container>
        </DrawerFilterStyles.ContentScrollable>
        <DrawerFilterStyles.ButtonsContainer>
          <Button variant={"contained"} onClick={onSubmit} color={"primary"} disabled={isSearching}>
            Search
          </Button>
          <Button variant={"outlined"} onClick={onReset} disabled={isSearching}>
            Reset
          </Button>
        </DrawerFilterStyles.ButtonsContainer>
      </DrawerFilterStyles.ContentWrapper>
    </Drawer>
  );
};
